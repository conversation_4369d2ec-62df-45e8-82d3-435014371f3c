"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserGroup = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_entity_1 = require("../../../common/entities/base.entity");
const user_entity_1 = require("../../users/entities/user.entity");
const role_entity_1 = require("../../roles/entities/role.entity");
let UserGroup = class UserGroup extends base_entity_1.BaseEntity {
};
exports.UserGroup = UserGroup;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户组名称' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        comment: '用户组名称',
    }),
    __metadata("design:type", String)
], UserGroup.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户组描述' }),
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: '用户组描述',
    }),
    __metadata("design:type", String)
], UserGroup.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1启用，0禁用' }),
    (0, typeorm_1.Column)({
        type: 'tinyint',
        default: 1,
        comment: '状态：1启用，0禁用',
    }),
    __metadata("design:type", Number)
], UserGroup.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => user_entity_1.User),
    (0, typeorm_1.JoinTable)({
        name: 'user_group_members',
        joinColumn: { name: 'user_group_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'user_id', referencedColumnName: 'id' },
    }),
    __metadata("design:type", Array)
], UserGroup.prototype, "users", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => role_entity_1.Role),
    (0, typeorm_1.JoinTable)({
        name: 'user_group_roles',
        joinColumn: { name: 'user_group_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' },
    }),
    __metadata("design:type", Array)
], UserGroup.prototype, "roles", void 0);
exports.UserGroup = UserGroup = __decorate([
    (0, typeorm_1.Entity)('user_groups'),
    (0, typeorm_1.Index)(['status'])
], UserGroup);
//# sourceMappingURL=user-group.entity.js.map