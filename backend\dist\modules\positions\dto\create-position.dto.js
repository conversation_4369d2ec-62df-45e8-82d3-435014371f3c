"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePositionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreatePositionDto {
}
exports.CreatePositionDto = CreatePositionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '岗位名称' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePositionDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '岗位编码' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePositionDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '上级岗位ID，0为根节点', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreatePositionDto.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '归属部门ID' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreatePositionDto.prototype, "departmentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '岗位类别：department部门，office科室' }),
    (0, class_validator_1.IsEnum)(['department', 'office']),
    __metadata("design:type", String)
], CreatePositionDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreatePositionDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1启用，0停用', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], CreatePositionDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '岗位描述', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePositionDto.prototype, "description", void 0);
//# sourceMappingURL=create-position.dto.js.map