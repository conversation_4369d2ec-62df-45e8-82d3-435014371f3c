import { BaseEntity } from '@/common/entities/base.entity';
import { Organization } from '@/modules/organizations/entities/organization.entity';
export declare class Position extends BaseEntity {
    name: string;
    code: string;
    parentId: number;
    parentPosition: Position;
    children: Position[];
    departmentId: number;
    department: Organization;
    type: string;
    sortOrder: number;
    status: number;
    description?: string;
}
