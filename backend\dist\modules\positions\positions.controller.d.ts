import { PositionsService } from './positions.service';
import { CreatePositionDto } from './dto/create-position.dto';
import { UpdatePositionDto } from './dto/update-position.dto';
import { QueryPositionDto } from './dto/query-position.dto';
import { Position } from './entities/position.entity';
import { ResponseDto } from '@/common/dto/response.dto';
import { User } from '@/modules/users/entities/user.entity';
export declare class PositionsController {
    private readonly positionsService;
    constructor(positionsService: PositionsService);
    create(createPositionDto: CreatePositionDto, currentUser: User): Promise<ResponseDto<Position>>;
    findAll(queryDto: QueryPositionDto): Promise<ResponseDto<Position[]>>;
    findTree(queryDto: QueryPositionDto): Promise<ResponseDto<Position[]>>;
    findOne(id: string): Promise<ResponseDto<Position>>;
    update(id: string, updatePositionDto: UpdatePositionDto, currentUser: User): Promise<ResponseDto<Position>>;
    remove(id: string): Promise<ResponseDto<null>>;
    batchRemove(body: {
        ids: number[];
    }): Promise<ResponseDto<null>>;
}
