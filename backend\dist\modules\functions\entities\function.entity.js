"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Function = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_entity_1 = require("../../../common/entities/base.entity");
const application_entity_1 = require("../../applications/entities/application.entity");
const role_entity_1 = require("../../roles/entities/role.entity");
let Function = class Function extends base_entity_1.BaseEntity {
};
exports.Function = Function;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '应用ID' }),
    (0, typeorm_1.Column)({
        name: 'application_id',
        type: 'bigint',
        comment: '应用ID',
    }),
    __metadata("design:type", Number)
], Function.prototype, "applicationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '上级功能ID，0为根节点' }),
    (0, typeorm_1.Column)({
        name: 'parent_id',
        type: 'bigint',
        default: 0,
        comment: '上级功能ID，0为根节点',
    }),
    __metadata("design:type", Number)
], Function.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '功能名称' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        comment: '功能名称',
    }),
    __metadata("design:type", String)
], Function.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '功能类型：directory目录，menu菜单，button按钮' }),
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['directory', 'menu', 'button'],
        default: 'menu',
        comment: '功能类型：directory目录，menu菜单，button按钮',
    }),
    __metadata("design:type", String)
], Function.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '功能路径' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 255,
        nullable: true,
        comment: '功能路径',
    }),
    __metadata("design:type", String)
], Function.prototype, "path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '功能图标' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        nullable: true,
        comment: '功能图标',
    }),
    __metadata("design:type", String)
], Function.prototype, "icon", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关联资源' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 255,
        nullable: true,
        comment: '关联资源',
    }),
    __metadata("design:type", String)
], Function.prototype, "resource", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序' }),
    (0, typeorm_1.Column)({
        name: 'sort_order',
        type: 'int',
        default: 0,
        comment: '排序',
    }),
    __metadata("design:type", Number)
], Function.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1启用，0停用' }),
    (0, typeorm_1.Column)({
        type: 'tinyint',
        default: 1,
        comment: '状态：1启用，0停用',
    }),
    __metadata("design:type", Number)
], Function.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => application_entity_1.Application, (app) => app.functions, { eager: true }),
    (0, typeorm_1.JoinColumn)({ name: 'application_id' }),
    __metadata("design:type", application_entity_1.Application)
], Function.prototype, "application", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Function, (func) => func.children),
    (0, typeorm_1.JoinColumn)({ name: 'parent_id' }),
    __metadata("design:type", Function)
], Function.prototype, "parent", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Function, (func) => func.parent),
    __metadata("design:type", Array)
], Function.prototype, "children", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => role_entity_1.Role, (role) => role.functions),
    __metadata("design:type", Array)
], Function.prototype, "roles", void 0);
exports.Function = Function = __decorate([
    (0, typeorm_1.Entity)('functions'),
    (0, typeorm_1.Index)(['applicationId']),
    (0, typeorm_1.Index)(['parentId']),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['sortOrder'])
], Function);
//# sourceMappingURL=function.entity.js.map