import { Repository } from 'typeorm';
import { Organization } from './entities/organization.entity';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';
import { QueryOrganizationDto } from './dto/query-organization.dto';
export declare class OrganizationsService {
    private organizationRepository;
    constructor(organizationRepository: Repository<Organization>);
    create(createOrganizationDto: CreateOrganizationDto, currentUserId?: number): Promise<Organization>;
    findAll(queryDto?: QueryOrganizationDto): Promise<Organization[]>;
    findTree(queryDto?: QueryOrganizationDto): Promise<Organization[]>;
    findOne(id: number): Promise<Organization>;
    update(id: number, updateOrganizationDto: UpdateOrganizationDto, currentUserId?: number): Promise<Organization>;
    remove(id: number): Promise<void>;
    batchRemove(ids: number[]): Promise<void>;
    private buildTree;
    private isDescendant;
    private getDescendants;
}
