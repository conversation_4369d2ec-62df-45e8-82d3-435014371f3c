import { AuthService } from './auth.service';
import { LoginDto, LoginResponseDto } from './dto/login.dto';
import { User } from '@/modules/users/entities/user.entity';
import { ResponseDto } from '@/common/dto/response.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(loginDto: LoginDto): Promise<ResponseDto<LoginResponseDto>>;
    logout(user: User): Promise<ResponseDto<any>>;
    getProfile(user: User): Promise<ResponseDto<any>>;
}
