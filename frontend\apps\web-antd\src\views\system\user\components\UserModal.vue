<script lang="ts" setup>
import { computed, ref, unref, watch } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { formSchema, buildTreeSelectData, buildSelectOptions } from '../user.data';
import {
  createUser,
  updateUser,
} from '../user.api';
import { getOrganizationTree } from '../../organization/organization.api';
import { getPositionList } from '../../position/position.api';
import { message } from 'ant-design-vue';
import { showLoading, showSuccess } from '#/utils/toast.js';

// 声明Emits
const emit = defineEmits(['register', 'success']);

const isUpdate = ref(false);
const userId = ref<number | null>(null);
const organizationTreeData = ref([]);
const positionOptions = ref([]);
const userPositions = ref([]);
const selectedPositionId = ref<number | undefined>();

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onClosed() {
    formApi.resetForm();
    isUpdate.value = false;
    userId.value = null;
    userPositions.value = [];
    selectedPositionId.value = undefined;
  },
  onConfirm() {},
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      // 加载组织树和岗位列表
      loadOrganizationTree();
      loadPositionList();

      const state = modalApi.useStore().value;
      if (state?.row) {
        // 编辑模式
        isUpdate.value = true;
        userId.value = state.row.id;
        formApi.setValues({
          ...state.row,
          password: undefined, // 编辑时不显示密码
        });
        // 加载用户岗位信息
        loadUserPositions(state.row.id);
      } else {
        // 新增模式
        isUpdate.value = false;
        userId.value = null;
        userPositions.value = [];
        if (state?.departmentId) {
          formApi.setFieldValue('departmentId', state.departmentId);
        }
      }
    }
  },
});

// 岗位表格配置
const positionGridOptions = {
  columns: userPositionColumns,
  height: 200,
  data: userPositions,
  rowConfig: {
    isHover: true,
  },
};

const [PositionGrid, positionGridApi] = useVbenVxeGrid({
  gridOptions: positionGridOptions,
});

// 加载组织树数据
const loadOrganizationTree = async () => {
  try {
    const data = await getOrganizationTree();
    organizationTreeData.value = buildTreeSelectData(data);

    // 更新表单schema中的组织树数据
    formApi.updateSchema([
      {
        fieldName: 'departmentId',
        componentProps: {
          treeData: organizationTreeData.value,
        },
      },
    ]);
  } catch (error) {
    console.error('加载组织树失败:', error);
  }
};

// 加载岗位列表
const loadPositionList = async () => {
  try {
    const data = await getPositionList();
    positionOptions.value = buildSelectOptions(data);

    // 更新表单schema中的岗位选项
    formApi.updateSchema([
      {
        fieldName: 'positionId',
        componentProps: {
          options: positionOptions.value,
        },
      },
    ]);
  } catch (error) {
    console.error('加载岗位列表失败:', error);
  }
};

// 加载用户岗位信息
const loadUserPositions = async (userId: number) => {
  try {
    // 这里应该调用获取用户岗位的API，暂时使用空数组
    userPositions.value = [];
  } catch (error) {
    console.error('加载用户岗位失败:', error);
  }
};

// 添加岗位
const handleAddPosition = () => {
  if (!selectedPositionId.value) {
    message.warning('请先选择岗位');
    return;
  }

  const position = positionOptions.value.find(p => p.value === selectedPositionId.value);
  if (!position) {
    message.warning('岗位信息不存在');
    return;
  }

  // 检查是否已存在
  const exists = userPositions.value.some(p => p.id === selectedPositionId.value);
  if (exists) {
    message.warning('该岗位已存在');
    return;
  }

  // 添加到岗位列表
  userPositions.value.push({
    id: selectedPositionId.value,
    name: position.label,
    code: '', // 这里需要从完整的岗位数据中获取
    department: { name: '' }, // 这里需要从完整的岗位数据中获取
  });

  // 清空岗位选择
  selectedPositionId.value = undefined;

  message.success('岗位添加成功');
};

// 删除岗位
const handleRemovePosition = (row: any) => {
  const index = userPositions.value.findIndex(p => p.id === row.id);
  if (index > -1) {
    userPositions.value.splice(index, 1);
    message.success('岗位删除成功');
  }
};

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'text-right pr-4',
    labelWidth: 100,
  },
  // 提交函数
  handleSubmit,
  handleReset,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: formSchema,
  wrapperClass: 'grid-cols-1',
  submitButtonOptions: {
    content: '确定',
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  },
  resetButtonOptions: {
    content: '取消',
  },
});

// 计算弹窗标题
const title = computed(() => {
  return isUpdate.value ? '编辑用户' : '新增用户';
});

// 表单重置事件
async function handleReset() {
  modalApi.close();
}

// 表单提交事件
async function handleSubmit(values: any) {
  try {
    showLoading('操作处理中...');

    // 添加用户岗位信息
    const submitData = {
      ...values,
      positionIds: userPositions.value.map(p => p.id),
    };

    if (isUpdate.value && userId.value) {
      await updateUser(userId.value, submitData);
      showSuccess('更新成功！');
    } else {
      await createUser(submitData);
      showSuccess('创建成功！');
    }

    modalApi.close();
    emit('success');
  } catch (error) {
    console.error('操作失败:', error);
    // message.error(error.message || '操作失败');
  }
}

// 打开新增弹窗
function openCreateModal(departmentId?: number) {
  isUpdate.value = false;
  userId.value = null;
  modalApi.open();

  if (departmentId) {
    formApi.setFieldValue('departmentId', departmentId);
  }
}

// 打开编辑弹窗
function openEditModal(record: any) {
  isUpdate.value = true;
  userId.value = record.id;
  modalApi.open();

  // 设置表单值
  formApi.setValues({
    ...record,
    password: undefined, // 编辑时不显示密码
  });
}

// 暴露方法给父组件
defineExpose({
  openCreateModal,
  openEditModal,
});


</script>

<template>
  <Modal
    v-bind="$attrs"
    :footer="false"
    :title="title"
    class="w-[800px]"
    :destroy-on-close="true"
    :maskClosable="false"
  >
    <div style="padding: 20px">
      <!-- 基础信息表单 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-4">基础信息</h3>
        <Form />
      </div>

      <!-- 岗位信息管理 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-4">岗位信息</h3>

        <!-- 添加岗位 -->
        <div class="mb-4 flex items-center gap-2">
          <a-select
            v-model="selectedPositionId"
            placeholder="请选择岗位"
            :options="positionOptions"
            show-search
            option-filter-prop="label"
            class="flex-1"
          />
          <VbenButton
            type="primary"
            @click="handleAddPosition"
          >
            添加
          </VbenButton>
        </div>

        <!-- 岗位列表 -->
        <PositionGrid>
          <template #action="{ row }">
            <a-button
              type="link"
              danger
              @click="handleRemovePosition(row)"
            >
              删除
            </a-button>
          </template>
        </PositionGrid>
      </div>
    </div>
  </Modal>
</template>

<style scoped lang="less">
@import '#/styles/dark-antd.less';
</style>
