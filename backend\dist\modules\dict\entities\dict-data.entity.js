"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DictData = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_entity_1 = require("../../../common/entities/base.entity");
let DictData = class DictData extends base_entity_1.BaseEntity {
};
exports.DictData = DictData;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '字典类型' }),
    (0, typeorm_1.Column)({
        name: 'dict_type',
        type: 'varchar',
        length: 100,
        comment: '字典类型',
    }),
    __metadata("design:type", String)
], DictData.prototype, "dictType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '字典标签' }),
    (0, typeorm_1.Column)({
        name: 'dict_label',
        type: 'varchar',
        length: 100,
        comment: '字典标签',
    }),
    __metadata("design:type", String)
], DictData.prototype, "dictLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '字典键值' }),
    (0, typeorm_1.Column)({
        name: 'dict_value',
        type: 'varchar',
        length: 100,
        comment: '字典键值',
    }),
    __metadata("design:type", String)
], DictData.prototype, "dictValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '父级ID，支持层级结构' }),
    (0, typeorm_1.Column)({
        name: 'parent_id',
        type: 'bigint',
        default: 0,
        comment: '父级ID，支持层级结构',
    }),
    __metadata("design:type", Number)
], DictData.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序' }),
    (0, typeorm_1.Column)({
        name: 'sort_order',
        type: 'int',
        default: 0,
        comment: '排序',
    }),
    __metadata("design:type", Number)
], DictData.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1启用，0停用' }),
    (0, typeorm_1.Column)({
        type: 'tinyint',
        default: 1,
        comment: '状态：1启用，0停用',
    }),
    __metadata("design:type", Number)
], DictData.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否默认：1是，0否' }),
    (0, typeorm_1.Column)({
        name: 'is_default',
        type: 'tinyint',
        default: 0,
        comment: '是否默认：1是，0否',
    }),
    __metadata("design:type", Number)
], DictData.prototype, "isDefault", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注' }),
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: '备注',
    }),
    __metadata("design:type", String)
], DictData.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => DictData, (dictData) => dictData.children),
    (0, typeorm_1.JoinColumn)({ name: 'parent_id' }),
    __metadata("design:type", DictData)
], DictData.prototype, "parent", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => DictData, (dictData) => dictData.parent),
    __metadata("design:type", Array)
], DictData.prototype, "children", void 0);
exports.DictData = DictData = __decorate([
    (0, typeorm_1.Entity)('dict_data'),
    (0, typeorm_1.Index)(['dictType']),
    (0, typeorm_1.Index)(['parentId']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['sortOrder'])
], DictData);
//# sourceMappingURL=dict-data.entity.js.map