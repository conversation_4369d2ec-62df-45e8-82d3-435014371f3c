"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FunctionsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const functions_service_1 = require("./functions.service");
const functions_controller_1 = require("./functions.controller");
const function_entity_1 = require("./entities/function.entity");
let FunctionsModule = class FunctionsModule {
};
exports.FunctionsModule = FunctionsModule;
exports.FunctionsModule = FunctionsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([function_entity_1.Function])],
        controllers: [functions_controller_1.FunctionsController],
        providers: [functions_service_1.FunctionsService],
        exports: [functions_service_1.FunctionsService],
    })
], FunctionsModule);
//# sourceMappingURL=functions.module.js.map