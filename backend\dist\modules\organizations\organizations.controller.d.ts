import { OrganizationsService } from './organizations.service';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';
import { QueryOrganizationDto } from './dto/query-organization.dto';
import { Organization } from './entities/organization.entity';
import { ResponseDto } from '@/common/dto/response.dto';
import { User } from '@/modules/users/entities/user.entity';
export declare class OrganizationsController {
    private readonly organizationsService;
    constructor(organizationsService: OrganizationsService);
    create(createOrganizationDto: CreateOrganizationDto, currentUser: User): Promise<ResponseDto<Organization>>;
    findAll(queryDto: QueryOrganizationDto): Promise<ResponseDto<Organization[]>>;
    findTree(queryDto: QueryOrganizationDto): Promise<ResponseDto<Organization[]>>;
    findOne(id: string): Promise<ResponseDto<Organization>>;
    update(id: string, updateOrganizationDto: UpdateOrganizationDto, currentUser: User): Promise<ResponseDto<Organization>>;
    remove(id: string): Promise<ResponseDto<void>>;
    batchRemove(body: {
        ids: number[];
    }): Promise<ResponseDto<void>>;
}
