"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const organizations_service_1 = require("./organizations.service");
const create_organization_dto_1 = require("./dto/create-organization.dto");
const update_organization_dto_1 = require("./dto/update-organization.dto");
const query_organization_dto_1 = require("./dto/query-organization.dto");
const organization_entity_1 = require("./entities/organization.entity");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
const response_dto_1 = require("../../common/dto/response.dto");
const user_entity_1 = require("../users/entities/user.entity");
let OrganizationsController = class OrganizationsController {
    constructor(organizationsService) {
        this.organizationsService = organizationsService;
    }
    async create(createOrganizationDto, currentUser) {
        const organization = await this.organizationsService.create(createOrganizationDto, currentUser.id);
        return response_dto_1.ResponseDto.success(organization, '创建成功');
    }
    async findAll(queryDto) {
        const organizations = await this.organizationsService.findAll(queryDto);
        return response_dto_1.ResponseDto.success(organizations, '获取成功');
    }
    async findTree(queryDto) {
        const tree = await this.organizationsService.findTree(queryDto);
        return response_dto_1.ResponseDto.success(tree, '获取成功');
    }
    async findOne(id) {
        const organization = await this.organizationsService.findOne(+id);
        return response_dto_1.ResponseDto.success(organization, '获取成功');
    }
    async update(id, updateOrganizationDto, currentUser) {
        const organization = await this.organizationsService.update(+id, updateOrganizationDto, currentUser.id);
        return response_dto_1.ResponseDto.success(organization, '更新成功');
    }
    async remove(id) {
        await this.organizationsService.remove(+id);
        return response_dto_1.ResponseDto.success(null, '删除成功');
    }
    async batchRemove(body) {
        await this.organizationsService.batchRemove(body.ids);
        return response_dto_1.ResponseDto.success(null, '批量删除成功');
    }
};
exports.OrganizationsController = OrganizationsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建组织' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: organization_entity_1.Organization }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_organization_dto_1.CreateOrganizationDto,
        user_entity_1.User]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取组织列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [organization_entity_1.Organization] }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_organization_dto_1.QueryOrganizationDto]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('tree'),
    (0, swagger_1.ApiOperation)({ summary: '获取组织树形结构' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [organization_entity_1.Organization] }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_organization_dto_1.QueryOrganizationDto]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "findTree", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取组织详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: organization_entity_1.Organization }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新组织' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: organization_entity_1.Organization }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_organization_dto_1.UpdateOrganizationDto,
        user_entity_1.User]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除组织' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "remove", null);
__decorate([
    (0, common_1.Delete)(),
    (0, swagger_1.ApiOperation)({ summary: '批量删除组织' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '批量删除成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "batchRemove", null);
exports.OrganizationsController = OrganizationsController = __decorate([
    (0, swagger_1.ApiTags)('组织管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('organizations'),
    __metadata("design:paramtypes", [organizations_service_1.OrganizationsService])
], OrganizationsController);
//# sourceMappingURL=organizations.controller.js.map