"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Role = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_entity_1 = require("../../../common/entities/base.entity");
const user_entity_1 = require("../../users/entities/user.entity");
const function_entity_1 = require("../../functions/entities/function.entity");
let Role = class Role extends base_entity_1.BaseEntity {
};
exports.Role = Role;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '角色名称' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        comment: '角色名称',
    }),
    __metadata("design:type", String)
], Role.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '角色编码' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        unique: true,
        comment: '角色编码',
    }),
    __metadata("design:type", String)
], Role.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '角色描述' }),
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: '角色描述',
    }),
    __metadata("design:type", String)
], Role.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1启用，0禁用' }),
    (0, typeorm_1.Column)({
        type: 'tinyint',
        default: 1,
        comment: '状态：1启用，0禁用',
    }),
    __metadata("design:type", Number)
], Role.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => user_entity_1.User, (user) => user.roles),
    __metadata("design:type", Array)
], Role.prototype, "users", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => function_entity_1.Function, (func) => func.roles),
    (0, typeorm_1.JoinTable)({
        name: 'role_functions',
        joinColumn: { name: 'role_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'function_id', referencedColumnName: 'id' },
    }),
    __metadata("design:type", Array)
], Role.prototype, "functions", void 0);
exports.Role = Role = __decorate([
    (0, typeorm_1.Entity)('roles'),
    (0, typeorm_1.Index)(['code']),
    (0, typeorm_1.Index)(['status'])
], Role);
//# sourceMappingURL=role.entity.js.map