import { BaseEntity } from '@/common/entities/base.entity';
import { Role } from '@/modules/roles/entities/role.entity';
export declare class Organization extends BaseEntity {
    name: string;
    code: string;
    parentId: number;
    type: string;
    sortOrder: number;
    defaultRoleId?: number;
    status: number;
    description?: string;
    parent?: Organization;
    children?: Organization[];
    defaultRole?: Role;
}
