import { Repository } from 'typeorm';
import { Position } from './entities/position.entity';
import { Organization } from '../organizations/entities/organization.entity';
import { CreatePositionDto } from './dto/create-position.dto';
import { UpdatePositionDto } from './dto/update-position.dto';
import { QueryPositionDto } from './dto/query-position.dto';
export declare class PositionsService {
    private positionRepository;
    private organizationRepository;
    constructor(positionRepository: Repository<Position>, organizationRepository: Repository<Organization>);
    create(createPositionDto: CreatePositionDto, currentUserId?: number): Promise<Position>;
    findAll(queryDto?: QueryPositionDto, relations?: string[]): Promise<Position[]>;
    findTree(queryDto?: QueryPositionDto): Promise<Position[]>;
    findOne(id: number): Promise<Position>;
    update(id: number, updatePositionDto: UpdatePositionDto, currentUserId?: number): Promise<Position>;
    remove(id: number): Promise<void>;
    batchRemove(ids: number[]): Promise<void>;
    private buildTree;
    private isDescendant;
    private getDescendants;
}
