"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysConfigController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const sys_config_service_1 = require("./sys-config.service");
let SysConfigController = class SysConfigController {
    constructor(sysConfigService) {
        this.sysConfigService = sysConfigService;
    }
};
exports.SysConfigController = SysConfigController;
exports.SysConfigController = SysConfigController = __decorate([
    (0, swagger_1.ApiTags)('系统配置'),
    (0, common_1.Controller)('sys-config'),
    __metadata("design:paramtypes", [sys_config_service_1.SysConfigService])
], SysConfigController);
//# sourceMappingURL=sys-config.controller.js.map