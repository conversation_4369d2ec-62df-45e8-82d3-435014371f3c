"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseEntity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
class BaseEntity {
}
exports.BaseEntity = BaseEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'bigint' }),
    __metadata("design:type", Number)
], BaseEntity.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    (0, typeorm_1.CreateDateColumn)({
        name: 'created_time',
        type: 'datetime',
        comment: '创建时间',
    }),
    __metadata("design:type", Date)
], BaseEntity.prototype, "createdTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updated_time',
        type: 'datetime',
        comment: '更新时间',
    }),
    __metadata("design:type", Date)
], BaseEntity.prototype, "updatedTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建人ID', required: false }),
    (0, typeorm_1.Column)({
        name: 'created_by',
        type: 'bigint',
        nullable: true,
        comment: '创建人',
    }),
    __metadata("design:type", Number)
], BaseEntity.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新人ID', required: false }),
    (0, typeorm_1.Column)({
        name: 'updated_by',
        type: 'bigint',
        nullable: true,
        comment: '更新人',
    }),
    __metadata("design:type", Number)
], BaseEntity.prototype, "updatedBy", void 0);
//# sourceMappingURL=base.entity.js.map