{"version": 3, "file": "role.entity.js", "sourceRoot": "", "sources": ["../../../../src/modules/roles/entities/role.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAAuE;AACvE,6CAA8C;AAC9C,sEAA2D;AAC3D,kEAA4D;AAC5D,8EAAwE;AAKjE,IAAM,IAAI,GAAV,MAAM,IAAK,SAAQ,wBAAU;CA6CnC,CAAA;AA7CY,oBAAI;AAOf;IANC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,GAAG;QACX,OAAO,EAAE,MAAM;KAChB,CAAC;;kCACW;AASb;IAPC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,EAAE;QACV,MAAM,EAAE,IAAI;QACZ,OAAO,EAAE,MAAM;KAChB,CAAC;;kCACW;AAQb;IANC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,MAAM;KAChB,CAAC;;yCACmB;AAQrB;IANC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,YAAY,EAAE,CAAC;IAC1C,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,YAAY;KACtB,CAAC;;oCACa;AAIf;IADC,IAAA,oBAAU,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;;mCAC9B;AAQf;IANC,IAAA,oBAAU,EAAC,GAAG,EAAE,CAAC,0BAAQ,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;IAChD,IAAA,mBAAS,EAAC;QACT,IAAI,EAAE,gBAAgB;QACtB,UAAU,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,IAAI,EAAE;QAC3D,iBAAiB,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,oBAAoB,EAAE,IAAI,EAAE;KACvE,CAAC;;uCACqB;eA5CZ,IAAI;IAHhB,IAAA,gBAAM,EAAC,OAAO,CAAC;IACf,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,CAAC;IACf,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;GACL,IAAI,CA6ChB"}