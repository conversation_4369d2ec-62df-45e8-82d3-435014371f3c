"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Application = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_entity_1 = require("../../../common/entities/base.entity");
const function_entity_1 = require("../../functions/entities/function.entity");
let Application = class Application extends base_entity_1.BaseEntity {
};
exports.Application = Application;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '应用名称' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        comment: '应用名称',
    }),
    __metadata("design:type", String)
], Application.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '应用Key' }),
    (0, typeorm_1.Column)({
        name: 'app_key',
        type: 'varchar',
        length: 100,
        unique: true,
        comment: '应用Key',
    }),
    __metadata("design:type", String)
], Application.prototype, "appKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '联系人' }),
    (0, typeorm_1.Column)({
        name: 'contact_person',
        type: 'varchar',
        length: 100,
        nullable: true,
        comment: '联系人',
    }),
    __metadata("design:type", String)
], Application.prototype, "contactPerson", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '联系电话' }),
    (0, typeorm_1.Column)({
        name: 'contact_phone',
        type: 'varchar',
        length: 20,
        nullable: true,
        comment: '联系电话',
    }),
    __metadata("design:type", String)
], Application.prototype, "contactPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1启用，0禁用' }),
    (0, typeorm_1.Column)({
        type: 'tinyint',
        default: 1,
        comment: '状态：1启用，0禁用',
    }),
    __metadata("design:type", Number)
], Application.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '应用描述' }),
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: '应用描述',
    }),
    __metadata("design:type", String)
], Application.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => function_entity_1.Function, (func) => func.application),
    __metadata("design:type", Array)
], Application.prototype, "functions", void 0);
exports.Application = Application = __decorate([
    (0, typeorm_1.Entity)('applications'),
    (0, typeorm_1.Index)(['appKey']),
    (0, typeorm_1.Index)(['status'])
], Application);
//# sourceMappingURL=application.entity.js.map