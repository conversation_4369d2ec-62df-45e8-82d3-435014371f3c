"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysConfig = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_entity_1 = require("../../../common/entities/base.entity");
let SysConfig = class SysConfig extends base_entity_1.BaseEntity {
};
exports.SysConfig = SysConfig;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '参数名称' }),
    (0, typeorm_1.Column)({
        name: 'config_name',
        type: 'varchar',
        length: 100,
        comment: '参数名称',
    }),
    __metadata("design:type", String)
], SysConfig.prototype, "configName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '参数键名' }),
    (0, typeorm_1.Column)({
        name: 'config_key',
        type: 'varchar',
        length: 100,
        unique: true,
        comment: '参数键名',
    }),
    __metadata("design:type", String)
], SysConfig.prototype, "configKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '参数键值' }),
    (0, typeorm_1.Column)({
        name: 'config_value',
        type: 'text',
        nullable: true,
        comment: '参数键值',
    }),
    __metadata("design:type", String)
], SysConfig.prototype, "configValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '系统内置：Y是，N否' }),
    (0, typeorm_1.Column)({
        name: 'config_type',
        type: 'enum',
        enum: ['Y', 'N'],
        default: 'N',
        comment: '系统内置：Y是，N否',
    }),
    __metadata("design:type", String)
], SysConfig.prototype, "configType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注' }),
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: '备注',
    }),
    __metadata("design:type", String)
], SysConfig.prototype, "remark", void 0);
exports.SysConfig = SysConfig = __decorate([
    (0, typeorm_1.Entity)('sys_config'),
    (0, typeorm_1.Index)(['configKey']),
    (0, typeorm_1.Index)(['configType'])
], SysConfig);
//# sourceMappingURL=sys-config.entity.js.map