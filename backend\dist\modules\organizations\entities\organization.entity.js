"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Organization = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_entity_1 = require("../../../common/entities/base.entity");
const role_entity_1 = require("../../roles/entities/role.entity");
let Organization = class Organization extends base_entity_1.BaseEntity {
};
exports.Organization = Organization;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '机构名称' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        comment: '机构名称',
    }),
    __metadata("design:type", String)
], Organization.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '机构编码' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        unique: true,
        comment: '机构编码',
    }),
    __metadata("design:type", String)
], Organization.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '上级机构ID，0为根节点' }),
    (0, typeorm_1.Column)({
        name: 'parent_id',
        type: 'bigint',
        default: 0,
        comment: '上级机构ID，0为根节点',
    }),
    __metadata("design:type", Number)
], Organization.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '类别：department部门，office科室' }),
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['department', 'office'],
        default: 'department',
        comment: '类别：department部门，office科室',
    }),
    __metadata("design:type", String)
], Organization.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序' }),
    (0, typeorm_1.Column)({
        name: 'sort_order',
        type: 'int',
        default: 0,
        comment: '排序',
    }),
    __metadata("design:type", Number)
], Organization.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '默认角色ID' }),
    (0, typeorm_1.Column)({
        name: 'default_role_id',
        type: 'bigint',
        nullable: true,
        comment: '默认角色ID',
    }),
    __metadata("design:type", Number)
], Organization.prototype, "defaultRoleId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1启用，0停用' }),
    (0, typeorm_1.Column)({
        type: 'tinyint',
        default: 1,
        comment: '状态：1启用，0停用',
    }),
    __metadata("design:type", Number)
], Organization.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '描述' }),
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: '描述',
    }),
    __metadata("design:type", String)
], Organization.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Organization, (org) => org.children),
    (0, typeorm_1.JoinColumn)({ name: 'parent_id' }),
    __metadata("design:type", Organization)
], Organization.prototype, "parent", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Organization, (org) => org.parent),
    __metadata("design:type", Array)
], Organization.prototype, "children", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => role_entity_1.Role),
    (0, typeorm_1.JoinColumn)({ name: 'default_role_id' }),
    __metadata("design:type", role_entity_1.Role)
], Organization.prototype, "defaultRole", void 0);
exports.Organization = Organization = __decorate([
    (0, typeorm_1.Entity)('organizations'),
    (0, typeorm_1.Index)(['parentId']),
    (0, typeorm_1.Index)(['code']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['sortOrder'])
], Organization);
//# sourceMappingURL=organization.entity.js.map