import { JwtService } from '@nestjs/jwt';
import { UsersService } from '@/modules/users/users.service';
import { User } from '@/modules/users/entities/user.entity';
import { LoginDto } from './dto/login.dto';
export declare class AuthService {
    private usersService;
    private jwtService;
    constructor(usersService: UsersService, jwtService: JwtService);
    validateUser(account: string, password: string): Promise<User | null>;
    login(loginDto: LoginDto): Promise<{
        roleType: number;
        name: string;
        token: string;
        account: string;
    }>;
    logout(_user: User): Promise<{
        message: string;
    }>;
    getProfile(userId: number): Promise<{
        id: number;
        username: string;
        realName: string;
        email: string;
        phone: string;
        avatar: string;
        department: import("../organizations/entities/organization.entity").Organization;
        position: import("../positions/entities/position.entity").Position;
        roles: import("../roles/entities/role.entity").Role[];
    }>;
}
