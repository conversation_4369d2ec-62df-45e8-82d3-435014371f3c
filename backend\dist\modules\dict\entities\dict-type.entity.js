"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DictType = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_entity_1 = require("../../../common/entities/base.entity");
const dict_data_entity_1 = require("./dict-data.entity");
let DictType = class DictType extends base_entity_1.BaseEntity {
};
exports.DictType = DictType;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '字典名称' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        comment: '字典名称',
    }),
    __metadata("design:type", String)
], DictType.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '字典类型' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        unique: true,
        comment: '字典类型',
    }),
    __metadata("design:type", String)
], DictType.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1启用，0停用' }),
    (0, typeorm_1.Column)({
        type: 'tinyint',
        default: 1,
        comment: '状态：1启用，0停用',
    }),
    __metadata("design:type", Number)
], DictType.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注' }),
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: '备注',
    }),
    __metadata("design:type", String)
], DictType.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => dict_data_entity_1.DictData, (dictData) => dictData.dictType),
    __metadata("design:type", Array)
], DictType.prototype, "dictData", void 0);
exports.DictType = DictType = __decorate([
    (0, typeorm_1.Entity)('dict_types'),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['status'])
], DictType);
//# sourceMappingURL=dict-type.entity.js.map