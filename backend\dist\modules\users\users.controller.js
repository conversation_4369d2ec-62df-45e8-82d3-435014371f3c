"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const users_service_1 = require("./users.service");
const create_user_dto_1 = require("./dto/create-user.dto");
const update_user_dto_1 = require("./dto/update-user.dto");
const query_user_dto_1 = require("./dto/query-user.dto");
const user_entity_1 = require("./entities/user.entity");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
const response_dto_1 = require("../../common/dto/response.dto");
let UsersController = class UsersController {
    constructor(usersService) {
        this.usersService = usersService;
    }
    async create(createUserDto, currentUser) {
        const user = await this.usersService.create(createUserDto, currentUser.id);
        return response_dto_1.ResponseDto.success(user, '创建成功');
    }
    async findAll(queryDto) {
        const result = await this.usersService.findAll(queryDto);
        return response_dto_1.ResponseDto.success(result, '获取成功');
    }
    async findOne(id) {
        const user = await this.usersService.findOne(+id);
        return response_dto_1.ResponseDto.success(user, '获取成功');
    }
    async update(id, updateUserDto, currentUser) {
        const user = await this.usersService.update(+id, updateUserDto, currentUser.id);
        return response_dto_1.ResponseDto.success(user, '更新成功');
    }
    async remove(id) {
        await this.usersService.remove(+id);
        return response_dto_1.ResponseDto.success(undefined, '删除成功');
    }
    async batchRemove(ids) {
        await this.usersService.batchRemove(ids);
        return response_dto_1.ResponseDto.success(undefined, '批量删除成功');
    }
    async resetPassword(id, password, currentUser) {
        await this.usersService.resetPassword(+id, password, currentUser.id);
        return response_dto_1.ResponseDto.success(undefined, '重置成功');
    }
    async updateStatus(id, status, currentUser) {
        await this.usersService.updateStatus(+id, status, currentUser.id);
        return response_dto_1.ResponseDto.success(undefined, '更新成功');
    }
    async batchUpdateStatus(body, currentUser) {
        await this.usersService.batchUpdateStatus(body.ids, body.status, currentUser.id);
        return response_dto_1.ResponseDto.success(undefined, '批量更新成功');
    }
    async batchResetPassword(body, currentUser) {
        await this.usersService.batchResetPassword(body.ids, body.password, currentUser.id);
        return response_dto_1.ResponseDto.success(undefined, '批量重置成功');
    }
    async assignRoles(id, body, currentUser) {
        await this.usersService.assignRoles(+id, body.roleIds, currentUser.id);
        return response_dto_1.ResponseDto.success(undefined, '分配成功');
    }
    async batchAssignRoles(body, currentUser) {
        await this.usersService.batchAssignRoles(body.userIds, body.roleIds, currentUser.id);
        return response_dto_1.ResponseDto.success(undefined, '批量分配成功');
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建用户' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: user_entity_1.User }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_dto_1.CreateUserDto,
        user_entity_1.User]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取用户列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_user_dto_1.QueryUserDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: user_entity_1.User }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新用户' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: user_entity_1.User }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_dto_1.UpdateUserDto,
        user_entity_1.User]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除用户' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "remove", null);
__decorate([
    (0, common_1.Delete)('batch'),
    (0, swagger_1.ApiOperation)({ summary: '批量删除用户' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '批量删除成功' }),
    __param(0, (0, common_1.Body)('ids')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "batchRemove", null);
__decorate([
    (0, common_1.Post)(':id/reset-password'),
    (0, swagger_1.ApiOperation)({ summary: '重置用户密码' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '重置成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('password')),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, user_entity_1.User]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "resetPassword", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: '更新用户状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('status')),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, user_entity_1.User]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Put)('batch-status'),
    (0, swagger_1.ApiOperation)({ summary: '批量更新用户状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '批量更新成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, user_entity_1.User]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "batchUpdateStatus", null);
__decorate([
    (0, common_1.Put)('reset-password/batch'),
    (0, swagger_1.ApiOperation)({ summary: '批量重置用户密码' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '批量重置成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, user_entity_1.User]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "batchResetPassword", null);
__decorate([
    (0, common_1.Put)('assign-roles/:id'),
    (0, swagger_1.ApiOperation)({ summary: '分配用户角色' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '分配成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, user_entity_1.User]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "assignRoles", null);
__decorate([
    (0, common_1.Put)('batch-assign-roles'),
    (0, swagger_1.ApiOperation)({ summary: '批量分配用户角色' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '批量分配成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, user_entity_1.User]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "batchAssignRoles", null);
exports.UsersController = UsersController = __decorate([
    (0, swagger_1.ApiTags)('用户管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('users'),
    __metadata("design:paramtypes", [users_service_1.UsersService])
], UsersController);
//# sourceMappingURL=users.controller.js.map