import { BaseEntity } from '@/common/entities/base.entity';
import { Application } from '@/modules/applications/entities/application.entity';
import { Role } from '@/modules/roles/entities/role.entity';
export declare class Function extends BaseEntity {
    applicationId: number;
    parentId: number;
    name: string;
    type: string;
    path?: string;
    icon?: string;
    resource?: string;
    sortOrder: number;
    status: number;
    application?: Application;
    parent?: Function;
    children?: Function[];
    roles?: Role[];
}
