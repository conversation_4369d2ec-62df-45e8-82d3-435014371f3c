"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const organization_entity_1 = require("./entities/organization.entity");
let OrganizationsService = class OrganizationsService {
    constructor(organizationRepository) {
        this.organizationRepository = organizationRepository;
    }
    async create(createOrganizationDto, currentUserId) {
        const existingOrg = await this.organizationRepository.findOne({
            where: { code: createOrganizationDto.code },
        });
        if (existingOrg) {
            throw new common_1.ConflictException('机构编码已存在');
        }
        if (createOrganizationDto.parentId && createOrganizationDto.parentId !== 0) {
            const parentOrg = await this.organizationRepository.findOne({
                where: { id: createOrganizationDto.parentId },
            });
            if (!parentOrg) {
                throw new common_1.NotFoundException('上级机构不存在');
            }
        }
        const organization = this.organizationRepository.create({
            ...createOrganizationDto,
            parentId: createOrganizationDto.parentId || 0,
            sortOrder: createOrganizationDto.sortOrder || 0,
            status: createOrganizationDto.status ?? 1,
            createdBy: currentUserId,
        });
        return this.organizationRepository.save(organization);
    }
    async findAll(queryDto) {
        const queryBuilder = this.organizationRepository.createQueryBuilder('org')
            .leftJoinAndSelect('org.defaultRole', 'role')
            .orderBy('org.sortOrder', 'ASC')
            .addOrderBy('org.createdTime', 'ASC');
        if (queryDto?.name) {
            queryBuilder.andWhere('org.name LIKE :name', { name: `%${queryDto.name}%` });
        }
        if (queryDto?.code) {
            queryBuilder.andWhere('org.code LIKE :code', { code: `%${queryDto.code}%` });
        }
        if (queryDto?.type) {
            queryBuilder.andWhere('org.type = :type', { type: queryDto.type });
        }
        if (queryDto?.status !== undefined) {
            queryBuilder.andWhere('org.status = :status', { status: queryDto.status });
        }
        if (queryDto?.parentId !== undefined) {
            queryBuilder.andWhere('org.parentId = :parentId', { parentId: queryDto.parentId });
        }
        const organizations = await queryBuilder.getMany();
        return organizations.map(org => ({
            ...org,
            id: Number(org.id),
            parentId: Number(org.parentId),
            defaultRoleName: org.defaultRole?.name || '',
        }));
    }
    async findTree(queryDto) {
        const organizations = await this.findAll(queryDto);
        return this.buildTree(organizations);
    }
    async findOne(id) {
        const organization = await this.organizationRepository.findOne({
            where: { id },
            relations: ['defaultRole', 'parent', 'children'],
        });
        if (!organization) {
            throw new common_1.NotFoundException('组织不存在');
        }
        return organization;
    }
    async update(id, updateOrganizationDto, currentUserId) {
        const organization = await this.findOne(id);
        if (updateOrganizationDto.code && updateOrganizationDto.code !== organization.code) {
            const existingOrg = await this.organizationRepository.findOne({
                where: { code: updateOrganizationDto.code },
            });
            if (existingOrg && existingOrg.id !== id) {
                throw new common_1.ConflictException('机构编码已存在');
            }
        }
        if (updateOrganizationDto.parentId && updateOrganizationDto.parentId !== 0) {
            if (updateOrganizationDto.parentId === id) {
                throw new common_1.ConflictException('不能将自己设置为上级机构');
            }
            const parentOrg = await this.organizationRepository.findOne({
                where: { id: updateOrganizationDto.parentId },
            });
            if (!parentOrg) {
                throw new common_1.NotFoundException('上级机构不存在');
            }
            if (await this.isDescendant(updateOrganizationDto.parentId, id)) {
                throw new common_1.ConflictException('不能将子机构设置为上级机构');
            }
        }
        Object.assign(organization, {
            ...updateOrganizationDto,
            updatedBy: currentUserId,
        });
        return this.organizationRepository.save(organization);
    }
    async remove(id) {
        const organization = await this.findOne(id);
        const children = await this.organizationRepository.find({
            where: { parentId: id },
        });
        if (children.length > 0) {
            throw new common_1.ConflictException('存在子机构，无法删除');
        }
        await this.organizationRepository.remove(organization);
    }
    async batchRemove(ids) {
        for (const id of ids) {
            await this.remove(id);
        }
    }
    buildTree(organizations, parentId = 0) {
        const tree = [];
        organizations.forEach(org => {
            const orgParentId = Number(org.parentId);
            const currentParentId = Number(parentId);
            if (orgParentId === currentParentId) {
                const children = this.buildTree(organizations, org.id);
                if (children.length > 0) {
                    org.children = children;
                }
                tree.push(org);
            }
        });
        return tree;
    }
    async isDescendant(ancestorId, descendantId) {
        const descendants = await this.getDescendants(descendantId);
        return descendants.some(desc => desc.id === ancestorId);
    }
    async getDescendants(parentId) {
        const children = await this.organizationRepository.find({
            where: { parentId },
        });
        let descendants = [...children];
        for (const child of children) {
            const childDescendants = await this.getDescendants(child.id);
            descendants = descendants.concat(childDescendants);
        }
        return descendants;
    }
};
exports.OrganizationsService = OrganizationsService;
exports.OrganizationsService = OrganizationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(organization_entity_1.Organization)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], OrganizationsService);
//# sourceMappingURL=organizations.service.js.map